/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UiHomeActions: typeof import('./src/monkey/components/ui-home-actions/ui-home-actions.vue')['default']
    UiHomeActionsTwo: typeof import('./src/monkey/components/ui-home-actions-two/ui-home-actions-two.vue')['default']
    UiHomeBuy: typeof import('./src/monkey/components/ui-home-buy/ui-home-buy.vue')['default']
    UiHomePrice: typeof import('./src/monkey/components/ui-home-price/ui-home-price.vue')['default']
    UiHomeRisk: typeof import('./src/monkey/components/ui-home-risk/ui-home-risk.vue')['default']
    UiHomeTitle: typeof import('./src/monkey/components/ui-home-title/ui-home-title.vue')['default']
    UiHomeTrading: typeof import('./src/monkey/components/ui-home-trading/ui-home-trading.vue')['default']
    UiUserVerify: typeof import('./src/monkey/components/ui-user-verify/ui-user-verify.vue')['default']
  }
}
