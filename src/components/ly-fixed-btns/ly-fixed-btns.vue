<template>
  <view :class="monkey.$helper.hasSafeArea() ? 'fixed-footer' : 'pb-4'" class="fixed bg-white bottom-0 w-full px-4 pt-4 z-10 shadow-lg">
    <div class="w-full bg-blue-500 text-white rounded-full h-12 flex items-center tracking-widest justify-center active:bg-blue-600 transition-all duration-300" @click="handleSubmit">
      {{ props.text }}
    </div>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  const props = withDefaults(
    defineProps<{
      text: string;
      type: 'primary' | 'secondary' | 'danger';
    }>(),
    {
      text: '提交',
      type: 'primary',
    },
  );

  const emit = defineEmits<{
    (e: 'submit'): void;
  }>();

  const handleSubmit = () => {
    emit('submit');
  };
</script>

<style lang="scss" scoped>
  .fixed-footer {
    padding-bottom: calc(env(safe-area-inset-bottom));
  }
</style>
