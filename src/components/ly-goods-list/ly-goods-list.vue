<template>
  <div class="grid grid-cols-2 gap-3">
    <div v-for="(product, index) in goodsList" :key="index" class="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 cursor-pointer group">
      <div class="relative aspect-square overflow-hidden">
        <image :src="product.image" :alt="product.name" fill class="object-cover group-hover:scale-105 transition-transform duration-300" />
        <div class="absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-bold text-white shadow-sm" :style="{ backgroundColor: '#ff4757' }">
          {{ product.discount }}
        </div>
        <div class="absolute top-2 right-2 flex flex-col space-y-1">
          <div v-for="(tag, tagIndex) in product.tags" :key="tagIndex" class="px-2 py-0.5 rounded-full text-xs font-medium bg-white/90 backdrop-blur-sm" :style="{ color: '#006dbb' }">
            {{ tag }}
          </div>
        </div>
      </div>

      <div class="p-3">
        <h3 class="text-sm font-medium text-gray-800 mb-2 line-clamp-2 leading-tight">
          {{ product.name }}
        </h3>

        <div class="flex items-baseline space-x-2 mb-2">
          <div class="flex items-baseline">
            <div class="text-lg font-bold text-red-600">
              {{ product.price }}
            </div>
            <div class="text-xs text-gray-500 ml-1">{{ product.unit }}</div>
          </div>
          <div class="text-xs text-gray-400 line-through">{{ product.originalPrice }}</div>
        </div>

        <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
          <div>已售 {{ product.sales.toLocaleString() }}</div>
          <div>{{ product.location }}</div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-1">
            <div class="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center">
              <div class="text-xs font-bold text-gray-600">店</div>
            </div>
          </div>
          <div class="text-xs text-gray-600 truncate flex-1">{{ product.shop }}</div>
          <div class="h-6 px-3 text-xs rounded text-white bg-blue-600 flex items-center justify-center">
            购买
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const goodsList = [
  {
    name: '优质花生米 精选大粒',
    originalPrice: '¥15.80',
    price: '¥12.50',
    unit: '/kg',
    sales: 2580,
    location: '山东济南',
    image:
      'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    discount: '8折',
    tags: ['现货', '包邮'],
    shop: '济南农产品专营店',
  },
  {
    name: '东北大米 五常稻花香',
    originalPrice: '¥12.00',
    price: '¥8.80',
    unit: '/kg',
    sales: 1250,
    location: '黑龙江',
    image:
      'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    discount: '限时',
    tags: ['预售', '顺丰'],
    shop: '东北粮油直营',
  },
  {
    name: '新疆棉花 长绒棉',
    originalPrice: '¥18.50',
    price: '¥15.20',
    unit: '/kg',
    sales: 890,
    location: '新疆',
    image:
      'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    discount: '特价',
    tags: ['现货', '质保'],
    shop: '新疆棉花基地',
  },
  {
    name: '优质玉米 饲料级',
    originalPrice: '¥4.20',
    price: '¥3.60',
    unit: '/kg',
    sales: 3200,
    location: '河南',
    image:
      'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    discount: '9折',
    tags: ['现货', '批发'],
    shop: '中原粮食批发',
  },
  {
    name: '有机大豆 非转基因',
    originalPrice: '¥9.90',
    price: '¥7.80',
    unit: '/kg',
    sales: 1680,
    location: '吉林',
    image:
      'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    discount: '热销',
    tags: ['有机', '包邮'],
    shop: '东北有机农场',
  },
  {
    name: '精品小麦 制粉专用',
    originalPrice: '¥5.50',
    price: '¥4.80',
    unit: '/kg',
    sales: 2100,
    location: '河北',
    image:
      'https://img0.baidu.com/it/u=1881163886,999953575&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
    discount: '直销',
    tags: ['现货', '优质'],
    shop: '华北粮食直供',
  },
]
</script>
<style scoped>
</style>