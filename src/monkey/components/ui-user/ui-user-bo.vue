<template>
  <div class="bg-white rounded-xl p-4 shadow-lg border border-gray-100">
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center">
        <div class="h-6 w-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mr-2">
          <text class="i-mdi-chart-box-plus-outline text-base text-white"></text>
        </div>
        <div class="text-base font-bold text-gray-800">业务概览</div>
      </div>
    </div>

    <!-- 采购业务 -->
    <div class="mb-4">
      <div class="text-sm font-medium text-gray-700 mb-3 flex items-center">
        <div class="h-3 w-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mr-2"></div>
        <span>采购订单</span>
        <div class="ml-auto text-xs text-gray-500 flex items-center" @click="handleAllOrders('buy', 'all')">
          全部订单
          <uni-icons type="right" size="12" color="#006dbb" class="ml-1"></uni-icons>
        </div>
      </div>
      <div class="grid grid-cols-4 gap-3">
        <div v-for="item in purchaseOrders" :key="item.name" class="text-center" @click="handleAllOrders('buy', item.name)">
          <div class="h-10 w-10 mx-auto mb-1.5 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center text-white shadow-md">
            <text :class="item.icon" class="iconfont !text-xl text-white"></text>
          </div>
          <div class="text-xs text-gray-600 mb-0.5">{{ item.name }}</div>
          <div class="text-sm font-bold text-[#006dbb]">{{ item.count }}</div>
        </div>
      </div>
    </div>

    <!-- 销售业务 -->
    <div>
      <div class="text-sm font-medium text-gray-700 mb-3 flex items-center">
        <div class="h-3 w-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full mr-2"></div>
        <span>销售订单</span>
        <div class="ml-auto text-xs text-gray-500 flex items-center" @click="handleAllOrders('sell', 'all')">
          全部订单
          <uni-icons type="right" size="12" color="#006dbb" class="ml-1"></uni-icons>
        </div>
      </div>
      <div class="grid grid-cols-4 gap-3">
        <div v-for="item in salesOrders" :key="item.name" class="text-center" @click="handleAllOrders('sell', item.name)">
          <div class="h-10 w-10 mx-auto mb-1.5 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-lg flex items-center justify-center text-white shadow-md">
            <text :class="item.icon" class="iconfont !text-xl text-white"></text>
          </div>
          <div class="text-xs text-gray-600 mb-0.5">{{ item.name }}</div>
          <div class="text-sm font-bold text-[#006dbb]">{{ item.count }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  interface Order {
    name: string;
    icon: string;
    count: string;
    status: number;
  }

  // 采购入库订单
  const purchaseOrders: Order[] = reactive([
    { name: '待确认', icon: 'icon-daiqueren1 ', count: '3', status : 0 },
    { name: '待付款', icon: 'icon-daifukuan', count: '8', status : 1 },
    { name: '待收货', icon: 'icon-daishouhuo', count: '145', status : 2 },
    { name: '已完成', icon: 'icon-yiwancheng-01', count: '0', status : 3 }, 
  ]);

  // 销售出库订单
  const salesOrders: Order[] = reactive([
    { name: '待确认', icon: 'icon-daiqueren1', count: '2', status : 0 },
    { name: '待收款', icon: 'icon-daishoukuan', count: '5', status : 1 },
    { name: '待发货', icon: 'icon-dengdaifahuo', count: '135', status : 2 },
    { name: '已完成', icon: 'icon-yiwancheng-01', count: '2', status : 3 },
  ]);

  const emit = defineEmits<{
    (e: 'allOrders'): void;
  }>();

  const handleAllOrders = (type: string, status: number) => {
    emit('allOrders', {
      type,
      status,
    });
  };
</script>
<style lang="scss" scoped></style>
