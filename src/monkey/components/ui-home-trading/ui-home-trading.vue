<template>
  <div class="rounded-lg  bg-white/80 backdrop-blur-sm border-0 shadow-md p-4">
    <slot name="title"></slot>
    <div class="mb-4">
      <div class="flex items-center space-x-2 mb-3">
        <div class="w-1 h-3 bg-blue-500 rounded-full"></div>
        <h3 class="text-xs font-medium text-gray-600">实时交易数据</h3>
      </div>

      <!-- 主要数据展示 -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="p-2 rounded-md bg-blue-50/70">
          <div class="flex flex-col">
            <div class="text-xs text-gray-500 mb-1 pl-1">累计交易额</div>
            <div class="text-xl font-bold text-blue-600 pl-1">74,047<span class="text-xs ml-1 font-normal text-blue-400">万元</span></div>
          </div>
        </div>
        <div class="p-2 rounded-md bg-sky-50/70">
          <div class="flex flex-col">
            <div class="text-xs text-gray-500 mb-1 pl-1">累计交易量</div>
            <div class="text-xl font-bold text-sky-600 pl-1">10,940<span class="text-xs ml-1 font-normal text-sky-400">吨</span></div>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="grid grid-cols-3 gap-3 mb-4">
        <div class="p-2 rounded-md bg-indigo-50/50 border-indigo-400">
          <div>
            <div class="text-xs text-gray-500 mb-1">订单数量</div>
            <div class="text-lg font-semibold text-indigo-600 flex items-baseline">
              1,167 <span class="text-xs ml-1 text-gray-400">笔</span>
            </div>
          </div>
        </div>
        <div class="p-2 rounded-md bg-sky-50/50 border-sky-400">
          <div>
            <div class="text-xs text-gray-500 mb-1">上线品种</div>
            <div class="text-lg font-semibold text-sky-600 flex items-baseline">
              122 <span class="text-xs ml-1 text-gray-400">个</span>
            </div>
          </div>
        </div>
        <div class="p-2 rounded-md bg-blue-50/50 border-blue-400">
          <div>
            <div class="text-xs text-gray-500 mb-1">注册商家</div>
            <div class="text-lg font-semibold text-blue-600 flex items-baseline">
              6,239 <span class="text-xs ml-1 text-gray-400">家</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 交易动态 -->
      <div class="p-3 rounded-md bg-blue-50/70 border-blue-400">
        <div class="flex flex-col">
          <div class="flex items-center space-x-1.5 mb-1.5">
            <div class="w-1.5 h-1.5 rounded-full animate-pulse bg-blue-500"></div>
            <div class="text-xs font-medium text-blue-600">实时交易动态</div>
          </div>
          <div class="text-xs text-blue-600/90 pl-3 truncate">
            张**下单采购花生139kg，金额12.07万元，06月15日
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>
</style>