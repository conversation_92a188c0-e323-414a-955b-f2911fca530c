<template>
  <div class="rounded-lg bg-white/80 backdrop-blur-md shadow-md px-4 py-5 flex flex-col items-center">
    <slot name="title"></slot>
    <div class="grid grid-cols-5 gap-4 pb-4 mb-4 border-b border-gray-100 w-full ">
      <div class="text-xs font-medium text-gray-600 text-center">品名</div>
      <div class="text-xs font-medium text-gray-600 text-center">规格</div>
      <div class="text-xs font-medium text-gray-600 text-center">市场</div>
      <div class="text-xs font-medium text-gray-600 text-center">价格</div>
      <div class="text-xs font-medium text-gray-600 text-center">涨跌幅</div>
    </div>
    <div class="space-y-3 w-full ">
      <div class="grid grid-cols-5 gap-2 items-center rounded-lg hover:bg-gray-50/70 transition-colors border-b border-gray-100 last:border-b-0 pb-3 last:pb-0" v-for="item in list" :key="item.name">
        <div class="text-xs text-gray-800 text-center truncate">{{ item.name }}</div>
        <div class="text-xs text-gray-600 text-center truncate">{{ item.spec }}</div>
        <div class="text-xs text-gray-600 text-center truncate">{{ item.market }}</div>
        <div class="text-xs font-bold text-center" :class="{'text-green-600': isPositiveOrZero(item.change), 'text-red-600': !isPositiveOrZero(item.change)}">{{ item.price }}</div>
        <div class="flex justify-center">
          <span class="inline-block px-3 py-1 rounded-full text-xs font-medium  transition-transform hover:scale-105" :class="{'text-green-600': isPositive(item.change), 'text-red-600': isNegative(item.change), 'text-gray-500': isZero(item.change)}">
            {{ item.change }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const list = [
  {
    name: '三七',
    spec: '筋条统大根',
    market: '文山市场',
    price: 120,
    change: '+4.35%',
  },
  {
    name: '三七',
    spec: '打粉类大根',
    market: '文山市场',
    price: 114,
    change: '+8.57%',
  },
  {
    name: '三七',
    spec: '本色冬七统',
    market: '文山市场',
    price: 150,
    change: '+0.00%',
  },
  {
    name: '三七',
    spec: '打粉类大根',
    market: '文山市场',
    price: 114,
    change: '-2.57%',
  },
]

// 判断涨跌幅是否为正数、负数或零
const isPositive = (change: string) =>
  change.startsWith('+') && change !== '+0.00%'
const isNegative = (change: string) =>
  change.startsWith('-') && change !== '-0.00%'
const isZero = (change: string) =>
  change === '+0.00%' || change === '-0.00%' || change === '0.00%'
const isPositiveOrZero = (change: string) => !isNegative(change)
</script>
        