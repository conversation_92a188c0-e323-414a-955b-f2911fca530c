<template>
  <div class="grid grid-cols-2 gap-2">
    <div class="bg-white/15 backdrop-blur-sm border border-white/20 rounded-lg p-2.5 shadow-md">
      <div class="flex items-center space-x-3">
        <div class="h-8 w-8  rounded-md flex items-center justify-center">
          <text class="icon-qiyerenzheng iconfont !text-xl text-white"></text>
        </div>
        <div class="flex-1 min-w-0">
          <div class="text-xs text-blue-100">企业认证</div>
          <div class="text-xs font-medium truncate">已通过</div>
        </div>
        <div class="h-8 w-8 rounded-md flex items-center justify-center">
          <uni-icons type="arrow-right" size="20" color="#fff"></uni-icons>
        </div>
      </div>
    </div>
    <div class="bg-white/15 backdrop-blur-sm border border-white/20 rounded-lg p-2.5 shadow-md">
      <div class="flex items-center space-x-3">
        <div class="h-8 w-8 rounded-md flex items-center justify-center">
          <text class="icon-gerenrenzheng iconfont !text-xl text-white"></text>
        </div>
        <div class="flex-1 min-w-0">
          <div class="text-xs text-blue-100">个人认证</div>
          <div class="text-xs font-medium truncate">已通过</div>
        </div>
        <div class="h-8 w-8 rounded-md flex items-center justify-center">
          <uni-icons type="arrow-right" size="20" color="#fff"></uni-icons>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped lang="scss"></style>