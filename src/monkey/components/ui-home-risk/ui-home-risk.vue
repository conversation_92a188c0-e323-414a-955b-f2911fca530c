<template>
  <section>
        <div class="flex items-center justify-between mb-4">
          <div class="text-base font-semibold text-gray-800">预警通知</div>
          <div class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium h-9 rounded-md px-3 text-blue-600 hover:text-blue-700">查看全部</div>
        </div>
        <div class="space-y-3">
          <div class="rounded-lg border border-blue-500 p-4 bg-white shadow-sm">
            <div class="flex items-start">
              <div class="p-2 rounded-lg mr-3 bg-blue-50 text-blue-600"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                  <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                  <path d="M12 9v4"></path>
                  <path d="M12 17h.01"></path>
                </svg></div>
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <h3 class="font-medium text-gray-800">A区库存预警</h3>
                  <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs border-blue-500 text-blue-600">紧急</div>
                </div>
                <p class="text-sm text-gray-600">原材料库存低于安全线</p>
              </div>
            </div>
          </div>
          <div class="rounded-lg border border-sky-400 p-4 bg-white shadow-sm">
            <div class="flex items-start">
              <div class="p-2 rounded-lg mr-3 bg-sky-50 text-sky-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                  <path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path>
                </svg></div>
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <h3 class="font-medium text-gray-800">温度监控</h3>
                  <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs border-sky-400 text-sky-600">警告</div>
                </div>
                <p class="text-sm text-gray-600">B2仓库温度异常</p>
              </div>
            </div>
          </div>
          <div class="rounded-lg border border-indigo-200 p-4 bg-white shadow-sm">
            <div class="flex items-start">
              <div class="p-2 rounded-lg mr-3 bg-indigo-50 text-indigo-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                  <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                  <path d="M12 11h4"></path>
                  <path d="M12 16h4"></path>
                  <path d="M8 11h.01"></path>
                  <path d="M8 16h.01"></path>
                </svg></div>
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <h3 class="font-medium text-gray-800">到期提醒</h3>
                  <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs border-indigo-200 text-indigo-600">提醒</div>
                </div>
                <p class="text-sm text-gray-600">3批次货物即将过期</p>
              </div>
            </div>
          </div>
        </div>
      </section>
</template>
<script setup lang="ts">

</script>