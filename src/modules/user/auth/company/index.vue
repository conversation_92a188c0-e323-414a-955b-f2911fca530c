<template>
  <ly-layout>
    <uni-nav-bar left-icon="left" leftWidth="200rpx" leftText="企业认证" :fixed="true" color="#fff" statusBar :border="false" />
    <!-- 步骤条 -->
    <view class="bg-gradient-to-r from-blue-600 to-indigo-600 px-4 pt-5 pb-4 text-white text-sm mb-4">
      <view class="w-full h-2 bg-blue-300 rounded-full mb-3 shadow-md">
        <view class="h-full bg-gradient-to-r from-teal-300 to-emerald-200 rounded-full shadow-sm" :style="{ width: `${(currentStep + 1) * 33.33}%` }"></view>
      </view>
      <view class="flex items-center">
        <view v-if="currentStep === 0">第1步：填写认证信息</view>
        <view v-if="currentStep === 1">第2步：等待审核</view>
        <view v-if="currentStep === 2">第3步：认证结果</view>
      </view>
    </view>
    <view class="px-4">
      <!-- 第一步：企业认证内容 -->
      <view v-if="currentStep === 0" class="rounded-lg shadow-sm">
        <!-- 温馨提示 -->
        <view class="bg-gradient-to-r from-yellow-50 to-amber-50 p-4 rounded-lg mb-5 border-l-4 border-amber-500 shadow-sm flex items-start">
          <uni-icons type="info-filled" color="#F59E0B" size="20" class="mr-3 mt-0.5 flex-shrink-0"></uni-icons>
          <view>
            <text class="block font-bold text-amber-700 mb-1">温馨提示</text>
            <text class="text-amber-700 text-sm leading-5 block"> 请填写真实有效的企业信息，审核通过后将不可修改。提交的资料仅用于平台认证使用，我们将严格保护您的信息安全。 </text>
          </view>
        </view>
        <!-- 表单内容 -->
        <uni-forms ref="formRef" :modelValue="formData" :rules="rules" :label-position="'top'" :label-width="200" :border="false">
          <!-- 1. 企业基础信息 -->
          <view class="mb-4 p-4 bg-white rounded-lg">
            <view class="text-sm font-bold mb-4 text-blue-600 pl-2 relative after:content-[''] after:absolute after:left-0 after:top-0 after:w-1 after:h-3 after:bg-blue-500 after:rounded-full after:top-1/2 after:-translate-y-1/2">企业基础信息</view>

            <!-- 企业类型 -->
            <uni-forms-item label="企业类型" name="companyType" required>
              <uni-data-checkbox v-model="formData.companyType" :localdata="companyTypeOptions" placeholder="请选择企业类型" mode="button" :border="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 上传营业执照 -->
            <uni-forms-item label="上传营业执照" name="businessLicense" required>
              <uni-file-picker v-model="formData.businessLicense" file-mediatype="image" limit="1" title="点击上传营业执照照片" :border="false"></uni-file-picker>
              <view class="text-gray-400 text-xs mt-1">请上传清晰的营业执照原件照片或扫描件</view>
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 企业名称 -->
            <uni-forms-item label="企业名称" name="companyName" required>
              <uni-easyinput v-model="formData.companyName" placeholder="请与营业执照上名称保持一致" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 统一社会信用代码 -->
            <uni-forms-item label="统一社会信用代码" name="creditCode" required>
              <uni-easyinput v-model="formData.creditCode" placeholder="请输入18位统一社会信用代码" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 联系电话 -->
            <uni-forms-item label="联系电话" name="phone" required>
              <uni-easyinput v-model="formData.phone" placeholder="请输入企业联系电话" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 详细地址 -->
            <uni-forms-item label="详细地址" name="address" required>
              <uni-easyinput v-model="formData.address" placeholder="请输入企业详细地址" type="textarea" :inputBorder="false" />
            </uni-forms-item>
          </view>

          <!-- 2. 法人信息 -->
          <view class="mb-6 p-4 bg-white rounded-lg">
            <view class="text-sm font-bold mb-4 text-green-600 pl-2 relative after:content-[''] after:absolute after:left-0 after:top-0 after:w-1 after:h-3 after:bg-green-500 after:rounded-full after:top-1/2 after:-translate-y-1/2">法人信息</view>

            <!-- 法人代表 -->
            <uni-forms-item label="法人代表" name="legalPerson" required>
              <uni-easyinput v-model="formData.legalPerson" placeholder="请输入法人代表姓名" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 法人身份证号 -->
            <uni-forms-item label="法人身份证号" name="legalPersonId" required>
              <uni-easyinput v-model="formData.legalPersonId" placeholder="请输入法人身份证号码" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 上传法人身份证 -->
            <uni-forms-item label="上传法人身份证" name="idCard" required>
              <uni-file-picker v-model="formData.idCard" file-mediatype="image" limit="2" title="请上传法人身份证正反面照片"></uni-file-picker>
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 法人手机号 -->
            <uni-forms-item label="法人手机号" name="legalPersonPhone" required>
              <uni-easyinput v-model="formData.legalPersonPhone" placeholder="请输入法人联系电话" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>
          </view>

          <!-- 3. 收款信息 -->
          <view class="p-4 bg-white rounded-lg">
            <view class="text-sm font-bold mb-4 text-amber-600 pl-2 relative after:content-[''] after:absolute after:left-0 after:top-0 after:w-1 after:h-3 after:bg-amber-500 after:rounded-full after:top-1/2 after:-translate-y-1/2">收款信息</view>

            <!-- 开户账号 -->
            <uni-forms-item label="开户账号" name="bankAccount" required>
              <uni-easyinput v-model="formData.bankAccount" placeholder="请输入企业银行账号" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 开户银行 -->
            <uni-forms-item label="开户银行" name="bankName" required>
              <uni-easyinput v-model="formData.bankName" placeholder="请输入开户银行名称" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 联系人电话 -->
            <uni-forms-item label="联系人电话" name="contactPhone" required>
              <uni-easyinput v-model="formData.contactPhone" placeholder="请输入联系人电话" trim="both" :inputBorder="false" />
              <div class="h-[1px] bg-gray-200 mt-2"></div>
            </uni-forms-item>

            <!-- 推荐人 -->
            <uni-forms-item label="推荐人" name="referrer">
              <uni-easyinput v-model="formData.referrer" placeholder="选填，如有推荐人请填写" trim="both" :inputBorder="false" />
            </uni-forms-item>
          </view>
        </uni-forms>
        <ly-line-bar :height="200" />
      </view>

      <!-- 第二步：审核中 -->
      <view v-if="currentStep === 1">
        <UnderReview />
      </view>
      <!-- 第三步：认证结果 -->
      <view v-if="currentStep === 2">
        <CertResults :authResult="authResult" />
      </view>
    </view>
    <ly-fixed-btns v-if="currentStep === 0" text="确认并提交" @submit="submitForm" />
  </ly-layout>
</template>

<script setup lang="ts">
  import UnderReview from '../components/under-review.vue';
  import CertResults from '../components/cert-results.vue';
  // 步骤条配置
  const stepsOptions = [{ title: '实名认证' }, { title: '审核中' }, { title: '认证结果' }];

  // 当前步骤
  const currentStep = ref(1);

  // 企业类型选项
  const companyTypeOptions = [
    { text: '有限责任公司', value: '1' },
    { text: '股份有限公司', value: '2' },
    { text: '个体工商户', value: '3' },
    { text: '合伙企业', value: '4' },
    { text: '其他', value: '5' },
  ];

  // 表单数据
  const formData = reactive({
    // 企业基础信息
    companyType: '',
    businessLicense: [],
    companyName: '',
    creditCode: '',
    phone: '',
    address: '',

    // 法人信息
    legalPerson: '',
    legalPersonId: '',
    legalPersonPhone: '',
    idCard: [],

    // 收款信息
    bankAccount: '',
    bankName: '',
    contactPhone: '',
    referrer: '',
  });

  // 表单校验规则
  const rules = {
    // 企业基础信息规则
    companyType: {
      rules: [{ required: true, errorMessage: '请选择企业类型' }],
    },
    businessLicense: {
      rules: [{ required: true, errorMessage: '请上传营业执照' }],
    },
    companyName: {
      rules: [{ required: true, errorMessage: '请输入企业名称' }],
    },
    creditCode: {
      rules: [
        { required: true, errorMessage: '请输入统一社会信用代码' },
        { pattern: /^[A-Z0-9]{18}$/, errorMessage: '请输入正确的统一社会信用代码' },
      ],
    },
    phone: {
      rules: [
        { required: true, errorMessage: '请输入联系电话' },
        { pattern: /^1[3456789]\d{9}$/, errorMessage: '请输入正确的手机号码' },
      ],
    },
    address: {
      rules: [{ required: true, errorMessage: '请输入详细地址' }],
    },

    // 法人信息规则
    legalPerson: {
      rules: [{ required: true, errorMessage: '请输入法人代表姓名' }],
    },
    legalPersonId: {
      rules: [
        { required: true, errorMessage: '请输入法人身份证号' },
        { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, errorMessage: '请输入正确的身份证号码' },
      ],
    },
    legalPersonPhone: {
      rules: [
        { required: true, errorMessage: '请输入法人手机号' },
        { pattern: /^1[3456789]\d{9}$/, errorMessage: '请输入正确的手机号码' },
      ],
    },
    idCard: {
      rules: [{ required: true, errorMessage: '请上传法人身份证照片' }],
    },

    // 收款信息规则
    bankAccount: {
      rules: [
        { required: true, errorMessage: '请输入开户账号' },
        { pattern: /^\d{16,19}$/, errorMessage: '请输入正确的银行账号' },
      ],
    },
    bankName: {
      rules: [{ required: true, errorMessage: '请输入开户银行名称' }],
    },
    contactPhone: {
      rules: [
        { required: true, errorMessage: '请输入联系人电话' },
        { pattern: /^1[3456789]\d{9}$/, errorMessage: '请输入正确的手机号码' },
      ],
    },
  };

  // 表单引用
  const formRef = ref();

  // 认证结果
  const authResult = reactive({
    status: 'success',
    reason: '',
  });

  // 提交表单
  const submitForm = async () => {
    console.log('submitForm');
    try {
      // 校验表单
      const valid = await formRef.value.validate();

      if (valid) {
        // 模拟提交数据到服务器
        uni.showLoading({
          title: '提交中...',
        });

        // 模拟提交延迟
        setTimeout(() => {
          uni.hideLoading();
          // 更新步骤
          currentStep.value = 1;

          // 模拟后台审核过程
          setTimeout(() => {
            // 更新为第三步
            currentStep.value = 2;

            // 这里可以根据实际情况设置审核结果
            authResult.status = 'success';
          }, 5000); // 为了演示，设置为5秒，实际可能需要几天
        }, 1500);
      }
    } catch (err) {
      console.error(err);
    }
  };
</script>

<style lang="scss" scoped>
  :deep(.uni-navbar__header) {
    @apply bg-transparent !important;
  }

  :deep(.uni-navbar__content) {
    @apply bg-gradient-to-r from-blue-600 to-indigo-600;
  }

  :deep(.uni-navbar-btn-text text) {
    font-size: 32rpx !important;
    font-weight: bolder !important;
    margin-left: 12rpx !important;
  }
</style>
