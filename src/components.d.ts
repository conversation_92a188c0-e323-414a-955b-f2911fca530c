/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UiHomeActions: typeof import('./monkey/components/ui-home/ui-home-actions.vue')['default']
    UiHomeActionsTwo: typeof import('./monkey/components/ui-home/ui-home-actions-two.vue')['default']
    UiHomeBuy: typeof import('./monkey/components/ui-home/ui-home-buy.vue')['default']
    UiHomePrice: typeof import('./monkey/components/ui-home/ui-home-price.vue')['default']
    UiHomeRisk: typeof import('./monkey/components/ui-home/ui-home-risk.vue')['default']
    UiHomeTitle: typeof import('./monkey/components/ui-home/ui-home-title.vue')['default']
    UiHomeTrading: typeof import('./monkey/components/ui-home/ui-home-trading.vue')['default']
    UiUserBo: typeof import('./monkey/components/ui-user/ui-user-bo.vue')['default']
    UiUserVerify: typeof import('./monkey/components/ui-user/ui-user-verify.vue')['default']
  }
}
