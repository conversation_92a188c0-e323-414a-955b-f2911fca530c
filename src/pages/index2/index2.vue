<template>
  <div class="flex flex-col w-full min-h-screen bg-gradient-to-br from-gray-50 to-green-50">
    <div class="sticky top-0 z-10  text-white px-4  pb-4 shadow-md bg-gradient-to-r from-green-600 to-emerald-600">
      <ly-status-bar />
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
          </div>
          <div>
            <div class="text-lg font-bold">智慧仓储</div>
            <div class="text-xs text-green-100">Warehouse Management</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-1 px-4 py-4 pb-20">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-800">仓储概览</h2>
        <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-primary/80 bg-green-100 text-green-700 border-green-200">实时更新</div>
      </div>
      <div class=" rounded-lg  h-40 mb-4 shadow-lg bg-gradient-to-r from-green-500 to-emerald-500">
        <image class="w-full h-full" src="/static/images/bg.png" />
      </div>

      <div class="grid grid-cols-2 gap-3 mb-4">
        <div class="rounded-lg border text-card-foreground p-4 bg-white shadow-sm border-l-4 border-green-500" data-v0-t="card">
          <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up h-8 w-8 text-green-500 bg-green-50 rounded-lg p-1.5 mr-3">
              <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
              <polyline points="16 7 22 7 22 13"></polyline>
            </svg>
            <div>
              <div class="text-base font-bold text-gray-800">我要买</div>
              <div class="text-xs text-gray-500">我要买</div>
            </div>
          </div>
        </div>
        <div class="rounded-lg border text-card-foreground p-4 bg-white shadow-sm border-l-4 border-emerald-500" data-v0-t="card">
          <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-truck h-8 w-8 text-emerald-500 bg-emerald-50 rounded-lg p-1.5 mr-3">
              <path d="M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2"></path>
              <path d="M15 18H9"></path>
              <path d="M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14"></path>
              <circle cx="17" cy="18" r="2"></circle>
              <circle cx="7" cy="18" r="2"></circle>
            </svg>
            <div>
              <div class="text-base font-bold text-gray-800">我要卖</div>
              <div class="text-xs text-gray-500">我要卖</div>
            </div>
          </div>
        </div>
      </div>

      <section>
        <h2 class="text-lg font-semibold text-gray-800 mb-4">快捷操作</h2>
        <div class="flex overflow-x-auto pb-2 space-x-4">
          <div class="rounded-lg border border-green-200 flex-shrink-0 w-28 p-4 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer">
            <div class="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800 mb-1">扫码入库</div>
              <div class="text-xs text-gray-500">快速登记</div>
            </div>
          </div>
          <div class="rounded-lg border border-emerald-200 flex-shrink-0 w-28 p-4 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer">
            <div class="h-12 w-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800 mb-1">批量出库</div>
              <div class="text-xs text-gray-500">批量处理</div>
            </div>
          </div>
          <div class="rounded-lg border border-teal-200 flex-shrink-0 w-28 p-4 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer">
            <div class="h-12 w-12 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800 mb-1">实时盘点</div>
              <div class="text-xs text-gray-500">库存核查</div>
            </div>
          </div>
          <div class="rounded-lg border border-green-300 flex-shrink-0 w-28 p-4 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer">
            <div class="h-12 w-12 bg-gradient-to-br from-green-600 to-emerald-700 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800 mb-1">数据报表</div>
              <div class="text-xs text-gray-500">分析统计</div>
            </div>
          </div>
          <div class="rounded-lg border border-emerald-300 flex-shrink-0 w-28 p-4 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer">
            <div class="h-12 w-12 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl flex items-center justify-center text-white mb-3 mx-auto"></div>
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800 mb-1">仓库地图</div>
              <div class="text-xs text-gray-500">位置导航</div>
            </div>
          </div>
        </div>
      </section>

      <section>
        <div class="flex items-center justify-between mb-4">
          <div class="text-lg font-semibold text-gray-800">预警通知</div><div
                  class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium h-9 rounded-md px-3 text-green-600 hover:text-green-700">查看全部</div>
        </div>
        <div class="space-y-3">
          <div class="rounded-lg border border-red-200 p-4 bg-white shadow-sm">
            <div class="flex items-start">
              <div class="p-2 rounded-lg mr-3 bg-red-50 text-red-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                  <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                  <path d="M12 9v4"></path>
                  <path d="M12 17h.01"></path>
                </svg></div>
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <h3 class="font-medium text-gray-800">A区库存预警</h3>
                  <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs border-red-200 text-red-600">紧急</div>
                </div>
                <p class="text-sm text-gray-600">原材料库存低于安全线</p>
              </div>
            </div>
          </div>
          <div class="rounded-lg border border-orange-200 p-4 bg-white shadow-sm">
            <div class="flex items-start">
              <div class="p-2 rounded-lg mr-3 bg-orange-50 text-orange-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                  <path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path>
                </svg></div>
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <h3 class="font-medium text-gray-800">温度监控</h3>
                  <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs border-orange-200 text-orange-600">警告</div>
                </div>
                <p class="text-sm text-gray-600">B2仓库温度异常</p>
              </div>
            </div>
          </div>
          <div class="rounded-lg border border-blue-200 p-4 bg-white shadow-sm">
            <div class="flex items-start">
              <div class="p-2 rounded-lg mr-3 bg-blue-50 text-blue-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                  <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                  <path d="M12 11h4"></path>
                  <path d="M12 16h4"></path>
                  <path d="M8 11h.01"></path>
                  <path d="M8 16h.01"></path>
                </svg></div>
              <div class="flex-1">
                <div class="flex items-center justify-between mb-1">
                  <h3 class="font-medium text-gray-800">到期提醒</h3>
                  <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs border-blue-200 text-blue-600">提醒</div>
                </div>
                <p class="text-sm text-gray-600">3批次货物即将过期</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>
.header {
  background: linear-gradient(
    to right,
    rgb(0, 109, 187),
    rgb(0, 128, 212),
    rgb(0, 109, 187)
  );
}

.swiper {
  background: linear-gradient(
    to right,
    rgb(0, 109, 187),
    rgb(0, 128, 212),
    rgb(77, 166, 224)
  );
}
</style>