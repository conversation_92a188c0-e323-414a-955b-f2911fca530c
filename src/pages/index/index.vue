<template>
  <div class="flex flex-col w-full min-h-screen bg-gradient-to-br from-blue-50 to-sky-50 ">
    <div class="arc-background"></div>
    <div class="header sticky top-0 z-20 text-white px-4 pb-4" :style="{ backgroundColor: navBarBgColor }">
      <ly-status-bar />
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center mr-3">
          </div>
          <div>
            <div class="text-lg font-bold">{{ monkey.$config.about.appName }}</div>
            <div class="text-xs text-green-100">{{ monkey.$config.about.slogan }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-1 px-4 b-20 relative z-10">
      <div class="swiper rounded-lg  h-40 mb-4 shadow-lg">
        <image class="size-full rounded-lg" src="https://img1.baidu.com/it/u=3499617352,759393670&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" />
      </div>
      <div class="mb-4">
        <ui-home-buy />
      </div>
      <div class="mb-4">
        <div class="text-base font-semibold text-gray-800 mb-4">快捷操作</div>
        <ui-home-actions />
      </div>
      <div class="mb-4">
        <div class="mb-4">
          <ui-home-title title="交易大厅" more="更多" />
        </div>
        <ui-home-trading>
        </ui-home-trading>
      </div>
      <div class="mb-4">
        <div class="mb-4">
          <ui-home-title title="价格动态" more="更多" />
        </div>
        <ui-home-price>
        </ui-home-price>
      </div>
      <div class="mb-4">
        <div class="mb-4">
          <ui-home-title title="商品推荐" more="更多" />
        </div>
        <ly-goods-list />
        <div class="mt-4 text-center">
          <Button variant="outline" size="sm" class="text-xs border-gray-200 hover:border-blue-300 bg-transparent px-6" :style="{ color: '#006dbb' }">
            加载更多商品
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入配置
import monkey from '@/monkey'
// 导航栏背景颜色
const navBarBgColor: Ref<string> = ref('rgba(0, 109, 187, 0)')
console.log()
/**
 * 监听页面滚动
 * @param e 滚动事件
 */
const handlePageScroll = (e: Page.PageScrollOption) => {
  const scrollTop = e.scrollTop
  // 设置滚动阈值，这里设为200，您可以根据需要调整
  const threshold = 200

  if (scrollTop > threshold) {
    // 超过阈值时设置不透明背景
    navBarBgColor.value = 'rgba(0, 109, 187, 1)'
  } else {
    // 计算透明度
    const opacity = scrollTop / threshold
    navBarBgColor.value = `rgba(0, 109, 187, ${opacity})`
  }
}

/**
 * 监听页面滚动
 * @param e 滚动事件
 */
onPageScroll((e: Page.PageScrollOption) => {
  handlePageScroll(e)
})
</script>

<style lang="scss" scoped>
.swiper {
  background: linear-gradient(
    to right,
    rgb(0, 109, 187),
    rgb(0, 128, 212),
    rgb(77, 166, 224)
  );
}

.arc-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 350px;
  background: linear-gradient(
    to bottom,
    rgb(0, 109, 187),
    rgb(0, 109, 187) 20%,
    rgba(0, 128, 212, 0.95) 50%,
    rgba(77, 166, 224, 0.7) 80%,
    rgba(77, 166, 224, 0) 100%
  );
  z-index: 0;
}

.arc-background::after {
  content: '';
  position: absolute;
  bottom: -40px;
  left: 0;
  width: 100%;
  height: 80px;
  background: radial-gradient(
    ellipse at center,
    rgba(77, 166, 224, 0.3) 0%,
    rgba(77, 166, 224, 0) 70%
  );
  border-radius: 50% 50% 0 0 / 100% 100% 0 0;
}
</style>