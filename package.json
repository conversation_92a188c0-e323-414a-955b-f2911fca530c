{"name": "uni-app-vite-vue3-tailwind-vscode-template", "version": "0.0.0", "packageManager": "pnpm@10.12.1", "scripts": {"dev": "uni -p mp-weixin", "build": "npm run build:mp-weixin", "dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "open:dev": "weapp open -p dist/dev/mp-weixin", "open:build": "weapp open -p dist/build/mp-weixin", "weapp:login": "weapp login", "upload:dev": "weapp upload -p dist/dev/mp-weixin -v 1.0.0 -d \"dev version\"", "upload:build": "weapp upload -p dist/build/mp-weixin -v 1.0.0 -d \"release version\"", "postinstall": "weapp-tw patch", "lint": "eslint .", "lint:fix": "eslint ./src --fix", "up:pkg": "pnpm up -rLi \"!@dcloudio/*\"", "up:uniapp": "pnpx @dcloudio/uvm@latest"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@vue/shared": "3.4.21", "pinia": "2.2.4", "vue": "^3.4.21", "vue-i18n": "^9.1.9"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@egoist/tailwindcss-icons": "^1.9.0", "@icebreakers/eslint-config": "^1.2.1", "@icebreakers/stylelint-config": "^1.1.0", "@iconify-json/svg-spinners": "^1.2.2", "@types/node": "^24.0.3", "@vue/runtime-core": "^3.4.21", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "postcss": "^8.5.6", "sass": "^1.89.2", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^0.27.5", "vite": "5.2.8", "weapp-ide-cli": "^3.0.0", "weapp-tailwindcss": "^4.1.10"}, "pnpm": {"onlyBuiltDependencies": ["vue-demi", "weapp-tailwindcss"]}}